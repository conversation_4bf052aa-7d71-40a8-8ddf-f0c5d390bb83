import sys, pathlib, time
from brother_ql.raster import BrotherQLRaster
from brother_ql.backends.helpers import send
from PIL import Image

# --- ANPASSA DETTA ---
MODEL = 'QL-570'          # din modell
LABEL = '62'              # rullbredd: '12', '29', '38', '50', '54', '62', '103' eller exakta DK-koder
PRINTER = 'usb://0x04f9:0x2015'  # pyusb/usb-adress; kör `python -m brother_ql.devicedesc` för att hitta
CUT_EACH = True           # autoskär mellan etiketter
# ----------------------

def render_image_to_label(qlr, img_path):
    img = Image.open(img_path).convert('1')  # binär bild; för QR brukar det bli skarpt
    qlr.exception_on_warning = True
    qlr.add_label(Image=img, label=LABEL, threshold=70, cut=CUT_EACH, rotate='auto')

def main(folder):
    folder = pathlib.Path(folder)
    files = sorted([p for p in folder.iterdir() if p.suffix.lower()=='.png'])
    if not files:
        print("Hittade inga .png i", folder)
        return

    qlr = BrotherQLRaster(MODEL)
    qlr.exception_on_warning = True

    for i, img in enumerate(files, 1):
        qlr.reset()
        render_image_to_label(qlr, img)
        print(f"[{i}/{len(files)}] {img.name}")
        send(instructions=qlr.data, printer_identifier=PRINTER, backend_identifier='pyusb', blocking=True)
        time.sleep(0.2)  # liten paus mellan etiketter

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Användning: python print_qr_labels.py /sökväg/till/mapp_med_png")
        sys.exit(1)
    main(sys.argv[1])