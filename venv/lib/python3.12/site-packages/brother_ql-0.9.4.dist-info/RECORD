../../../bin/brother_ql,sha256=5yvdliAon6L9npCbm-o1TEc-o1AVi8cR3BN5SWTaQeY,249
../../../bin/brother_ql_analyse,sha256=PMq2zoYkhcQ2URCs6KK5nhKRia5mZa18gHyAFeO1yJQ,266
../../../bin/brother_ql_create,sha256=I3-BwrpGoqtLo1NETnAfHEd-20JRhDEfvJeOC5RHNeo,265
../../../bin/brother_ql_debug,sha256=SIXxx5BHfKF2UN2gEHE8bfVJ0zg8vEMWqzCfPmHizCk,264
../../../bin/brother_ql_info,sha256=PXOIqSGx2WgOGXbTDsgJnMnBwUzLREYjR02EBCaaY_o,263
../../../bin/brother_ql_print,sha256=X3baY_Vu4HX95GoYOEKRaMP9RjtH01y4vhupxL1Lwmg,264
brother_ql-0.9.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
brother_ql-0.9.4.dist-info/METADATA,sha256=q8ku0BRxor7FRh6URFdeHUguBdQj6ALl82OXDXXKhiM,13390
brother_ql-0.9.4.dist-info/RECORD,,
brother_ql-0.9.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
brother_ql-0.9.4.dist-info/WHEEL,sha256=gduuPyBvFJQSQ0zdyxF7k0zynDXbIbvg5ZBHoXum5uk,110
brother_ql-0.9.4.dist-info/entry_points.txt,sha256=n2X8YZTDXuCMH7uTjZefIy3ruhsgVKTKs6zY-t7M2Kk,315
brother_ql-0.9.4.dist-info/top_level.txt,sha256=LctTw0-wZvV45I0PF_F3jTjUrhypBvawIy2mpkjtiuI,11
brother_ql-0.9.4.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
brother_ql/__init__.py,sha256=TFl72DwPoP8uVOY5VuCliWEgUmUYjyKSnN3cKKdQGkY,110
brother_ql/__pycache__/__init__.cpython-312.pyc,,
brother_ql/__pycache__/brother_ql_analyse.cpython-312.pyc,,
brother_ql/__pycache__/brother_ql_create.cpython-312.pyc,,
brother_ql/__pycache__/brother_ql_debug.cpython-312.pyc,,
brother_ql/__pycache__/brother_ql_info.cpython-312.pyc,,
brother_ql/__pycache__/brother_ql_print.cpython-312.pyc,,
brother_ql/__pycache__/cli.cpython-312.pyc,,
brother_ql/__pycache__/conversion.cpython-312.pyc,,
brother_ql/__pycache__/devicedependent.cpython-312.pyc,,
brother_ql/__pycache__/exceptions.cpython-312.pyc,,
brother_ql/__pycache__/helpers.cpython-312.pyc,,
brother_ql/__pycache__/image_trafos.cpython-312.pyc,,
brother_ql/__pycache__/labels.cpython-312.pyc,,
brother_ql/__pycache__/models.cpython-312.pyc,,
brother_ql/__pycache__/output_helpers.cpython-312.pyc,,
brother_ql/__pycache__/raster.cpython-312.pyc,,
brother_ql/__pycache__/reader.cpython-312.pyc,,
brother_ql/backends/__init__.py,sha256=pSJptWpIRHkxOuZl_ru2EfT2kl5QRazeAztFQWurbtA,1567
brother_ql/backends/__pycache__/__init__.cpython-312.pyc,,
brother_ql/backends/__pycache__/generic.cpython-312.pyc,,
brother_ql/backends/__pycache__/helpers.cpython-312.pyc,,
brother_ql/backends/__pycache__/linux_kernel.cpython-312.pyc,,
brother_ql/backends/__pycache__/network.cpython-312.pyc,,
brother_ql/backends/__pycache__/pyusb.cpython-312.pyc,,
brother_ql/backends/generic.py,sha256=LRYR8Nd-K92z-CQ3qUX-Iq07uAM4ztqtYiW8iTYc-1Q,1405
brother_ql/backends/helpers.py,sha256=ZrcA7OSIVccZlq-lPc-RFwE9M5tjP3cO9UE2HveH428,4114
brother_ql/backends/linux_kernel.py,sha256=O30Gzpkl_mZqsoyqA1PmZqcIVp6ewPWoxSn6BvQ1-JU,2839
brother_ql/backends/network.py,sha256=jV1UNXrPjwLK0XIgjrQoefyK_wJWH1aa48fCw92AZck,3563
brother_ql/backends/pyusb.py,sha256=6jiZj9TA_QM046RY8pTcstcvZ_cJB_GlLlzxLWeePr8,5879
brother_ql/brother_ql_analyse.py,sha256=2rtXoMPu9CUhzUmHCYwhBCRPnucU0zl1bj9nRCicuN4,685
brother_ql/brother_ql_create.py,sha256=_AOpvwyA0zfQbAYWJQskDTFKxF35Uyn4ZxcrYY69i7s,3509
brother_ql/brother_ql_debug.py,sha256=RtVdODZSlvt14iep6KAithbcnQLsrRESH9bYJtCU9JI,5114
brother_ql/brother_ql_info.py,sha256=UbXt5ljeS9Pkbk_HSoqNCHtxNaYMAPpexh3MJeh7EJw,1718
brother_ql/brother_ql_print.py,sha256=_j3qUbhUwiGrfLJHaYFddT3nmqC5rcn6MZEMIlwIQFY,3640
brother_ql/cli.py,sha256=GEL9bPBvX7Wkp7-UCNXhOk49bobwTtpyuHXc5OX0_0M,7780
brother_ql/conversion.py,sha256=iCBSzmVohB1T50muzMmUYOKEscN65cVLPgRkA4-o8lE,7385
brother_ql/devicedependent.py,sha256=KQ-kqVZK8SgECIF4BdGw3eAMojnnfKdHxifzizozFKo,3305
brother_ql/exceptions.py,sha256=7TeVpPVIV01FV66ifNMMOUJN688fXO-l3dAqgY_sMoQ,209
brother_ql/helpers.py,sha256=Xp0SBVyj1uvPuFsljydSKr-zIiwFoTuxJ5zVXPXk6O0,1269
brother_ql/image_trafos.py,sha256=O-x5jpmplSjKnU8l5FABKvw_5r9yAmKvTSTvOrwM2Ks,672
brother_ql/labels.py,sha256=dMOANboFMKoP4Bts2j0wxe88_QK_3BIyoWv5zWZAxcY,5658
brother_ql/models.py,sha256=JRE0wS5NvXQLAyZE8vso4hm1zCN55WtTM33CCzZ6Uqg,2738
brother_ql/output_helpers.py,sha256=k557UfMSVqYiDybYsFxlz02rxtq-Xv0GNP-W7uaX-fU,1525
brother_ql/raster.py,sha256=2XLWaAE45GkGugarSTr6cyN3bMTYH-TT9TEFeg5BlyM,7902
brother_ql/reader.py,sha256=hriOzBZWH_jVvh0QhhNd82b70vTYlsgEcpvrArW7eP0,13363
