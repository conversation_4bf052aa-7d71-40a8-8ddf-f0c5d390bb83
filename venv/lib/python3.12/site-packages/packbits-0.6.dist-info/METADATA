Metadata-Version: 2.2
Name: packbits
Version: 0.6
Summary: PackBits encoder/decoder
Home-page: https://github.com/kmike/packbits
Author: <PERSON>
Author-email: <EMAIL>
License: MIT License
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.2
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
License-File: AUTHORS.rst
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: summary

PackBits encoder/decoder
========================

This module implements a PackBits encoder/decoder for Python 2.x and 3.x.

PackBits encoding is used in PSD and TIFF files.

Installation
------------

::

    pip install packbits

Usage
-----

Import ``packbits`` module; then use ``packbits.encode(data)`` and
``packbits.decode(data)`` functions.

Contributing
------------

Development happens at github and bitbucket:

* https://github.com/kmike/packbits
* https://bitbucket.org/kmike/packbits

The main issue tracker is at github: https://github.com/kmike/packbits/issues

Feel free to submit ideas, bugs, pull requests (git or hg) or regular patches.

In order to run tests, install `tox <http://tox.testrun.org>`_ and type

::

    tox

from the source checkout.

The license is MIT.
